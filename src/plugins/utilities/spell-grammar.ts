import { BasePlugin, PluginConfig } from '../base-plugin';
import type { Editor } from '../../types';

/**
 * Spell/Grammar Checker plugin configuration
 */
const config: PluginConfig = {
  id: 'spell-grammar',
  name: 'Spell & Grammar Check',
  description: 'Check spelling and grammar in the editor content',
  version: '1.0.0',
  toolbarItems: [
    {
      id: 'spell-grammar',
      command: 'spell-grammar',
      icon: '✓📝',
      label: 'Spell Check',
      tooltip: 'Check spelling and grammar',
      group: 'utilities',
      ariaLabel: 'Check spelling and grammar',
    }
  ],
  shortcuts: [] // No shortcuts for spell check
};

/**
 * Interface for spelling errors
 */
interface SpellingError {
  word: string;
  position: { start: number; end: number };
  suggestions: string[];
}

/**
 * Spell/Grammar Checker plugin implementation
 * Provides spell checking and grammar suggestions
 */
export class SpellGrammarPlugin extends BasePlugin {
  private dialog: HTMLElement | null = null;
  private isDialogOpen = false;
  private isChecking = false;
  private markings: HTMLSpanElement[] = [];
  private errors: SpellingError[] = [];
  private currentErrorIndex = 0;
  private dictionary: Set<string> = new Set();

  constructor() {
    super(config);
  }

  /**
   * Initialize the plugin
   * @param editor The editor instance
   */
  public init(editor: Editor): void {
    super.init(editor);

    // Load dictionary if browser supports spell checking
    this.loadDictionary();
  }

  /**
   * Load dictionary for spell checking
   * In a real implementation, this would load a comprehensive dictionary
   * or connect to a spell checking API
   */
  private loadDictionary(): void {
    // Common English words - this is just a small sample
    // In a real implementation, this would be a comprehensive dictionary
    // or you would use a spelling API
    const commonWords = [
      'the', 'be', 'to', 'of', 'and', 'a', 'in', 'that', 'have', 'I',
      'it', 'for', 'not', 'on', 'with', 'he', 'as', 'you', 'do', 'at',
      'this', 'but', 'his', 'by', 'from', 'they', 'we', 'say', 'her', 'she',
      'or', 'an', 'will', 'my', 'one', 'all', 'would', 'there', 'their', 'what',
      'so', 'up', 'out', 'if', 'about', 'who', 'get', 'which', 'go', 'me',
      'when', 'make', 'can', 'like', 'time', 'no', 'just', 'him', 'know', 'take',
      'people', 'into', 'year', 'your', 'good', 'some', 'could', 'them', 'see', 'other',
      'than', 'then', 'now', 'look', 'only', 'come', 'its', 'over', 'think', 'also',
      'back', 'after', 'use', 'two', 'how', 'our', 'work', 'first', 'well', 'way',
      'even', 'new', 'want', 'because', 'any', 'these', 'give', 'day', 'most', 'us',
      'is', 'are', 'was', 'were', 'been', 'being', 'am', 'has', 'had', 'having',
      'does', 'did', 'doing', 'those', 'such', 'through', 'much', 'before', 'between',
      'same', 'should', 'could', 'would', 'might', 'may', 'must', 'shall'
    ];

    // Add common words to dictionary
    commonWords.forEach(word => this.dictionary.add(word.toLowerCase()));
  }

  /**
   * Handle the spell check command
   * @param _command The command to handle
   */
  public handleCommand(_command: string): void {
    if (_command === 'spell-grammar') {
      if (this.isDialogOpen) {
        this.closeDialog();
      } else {
        this.startSpellCheck();
      }
    }
  }

  /**
   * Start the spell checking process
   */
  private startSpellCheck(): void {
    if (!this.editor || this.isChecking) return;

    // Clear previous markings
    this.clearMarkings();

    // Show checking dialog
    this.showCheckingDialog();

    // Start the check after a short delay to allow the UI to update
    setTimeout(() => {
      this.performSpellCheck();
    }, 100);
  }

  /**
   * Show a dialog indicating that spell checking is in progress
   */
  private showCheckingDialog(): void {
    // Create backdrop
    const backdrop = document.createElement('div');
    backdrop.className = 'fixed inset-0 w-full h-full bg-black/40 z-[9998]';
    document.body.appendChild(backdrop);

    // Create dialog
    this.dialog = document.createElement('div');
    this.dialog.className = 'fixed top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-auto bg-white dark:bg-slate-800 rounded-lg shadow-xl p-6 z-[9999] font-sans text-gray-900 dark:text-slate-200';
    this.dialog.setAttribute('role', 'alertdialog'); // More appropriate for a status dialog
    this.dialog.setAttribute('aria-live', 'assertive');

    // Create dialog content
    const checkingContainer = document.createElement('div');
    checkingContainer.className = 'flex items-center justify-center text-gray-600 dark:text-slate-300 text-sm';

    const spinner = document.createElement('div');
    spinner.className = 'border-4 border-gray-200 dark:border-slate-600 border-t-blue-500 rounded-full w-6 h-6 animate-spin mr-3';
    checkingContainer.appendChild(spinner);

    const text = document.createElement('div');
    text.textContent = 'Checking spelling and grammar...';
    checkingContainer.appendChild(text);

    this.dialog.appendChild(checkingContainer);

    // Add dialog to document
    document.body.appendChild(this.dialog);
    this.isDialogOpen = true;
    this.isChecking = true;
  }

  /**
   * Perform the actual spell checking
   */
  private performSpellCheck(): void {
    if (!this.editor) return;

    this.errors = [];

    // Get all text nodes in the editor
    const textNodes = this.getTextNodes(this.editor.getElement());

    // Process each text node
    textNodes.forEach(node => {
      if (!node.textContent) return;

      const text = node.textContent;
      const words = text.match(/\b[a-zA-Z']+\b/g) || [];

      // Check each word
      words.forEach(word => {
        if (this.isSpelledCorrectly(word)) return;

        const wordStart = text.indexOf(word);
        if (wordStart === -1) return;

        const wordEnd = wordStart + word.length;

        // Generate suggestions
        const suggestions = this.generateSuggestions(word);

        // Add to errors list
        this.errors.push({
          word,
          position: { start: wordStart, end: wordEnd },
          suggestions
        });

        // Mark the misspelled word in the DOM
        this.markMisspelledWord(node, word, wordStart, wordEnd);
      });
    });

    // Update dialog with results
    this.updateResultsDialog();
  }

  /**
   * Get all text nodes in an element
   * @param element The element to scan
   * @returns Array of text nodes
   */
  private getTextNodes(element: HTMLElement): Text[] {
    const textNodes: Text[] = [];
    const walker = document.createTreeWalker(
      element,
      NodeFilter.SHOW_TEXT,
      null
    );

    let node: Node | null;
    while ((node = walker.nextNode()) !== null) {
      if (node.nodeType === Node.TEXT_NODE && node.textContent?.trim()) {
        textNodes.push(node as Text);
      }
    }

    return textNodes;
  }

  /**
   * Check if a word is spelled correctly
   * @param word The word to check
   * @returns True if correct, false if misspelled
   */
  private isSpelledCorrectly(word: string): boolean {
    // In a real implementation, this would use a spell checking API or library
    // For simplicity, we're just checking against our small dictionary

    // Remove any punctuation
    const cleanWord = word.replace(/[^a-zA-Z']/g, '').toLowerCase();

    // Skip very short words
    if (cleanWord.length < 3) return true;

    // Check in dictionary
    return this.dictionary.has(cleanWord);
  }

  /**
   * Generate spelling suggestions for a word
   * @param word The misspelled word
   * @returns Array of suggestions
   */
  private generateSuggestions(word: string): string[] {
    // In a real implementation, this would use a spell checking API
    // Here we're just generating some mock suggestions

    const lowerWord = word.toLowerCase();

    // Simple algorithm to generate "suggestions" - just for demo purposes
    // A real implementation would use a spelling API or more sophisticated algorithm
    const suggestions: string[] = [];

    // Add a capitalized version
    if (lowerWord !== word) {
      suggestions.push(lowerWord);
    }

    // Add a version with swapped adjacent letters
    for (let i = 0; i < word.length - 1; i++) {
      const swapped =
        word.substring(0, i) +
        word.charAt(i + 1) +
        word.charAt(i) +
        word.substring(i + 2);
      suggestions.push(swapped);
    }

    // Add a version with a single letter removed
    for (let i = 0; i < word.length; i++) {
      const removed = word.substring(0, i) + word.substring(i + 1);
      suggestions.push(removed);
    }

    // Basic additional variations
    suggestions.push(word + 's');
    suggestions.push(word + 'ed');
    suggestions.push(word + 'ing');

    // Filter out duplicates and limit to 5 suggestions
    return [...new Set(suggestions)].slice(0, 5);
  }

  /**
   * Mark a misspelled word in the DOM
   * @param node The text node containing the word
   * @param word The misspelled word
   * @param start Start index of the word
   * @param end End index of the word
   */
  private markMisspelledWord(node: Text, word: string, start: number, end: number): void {
    const parent = node.parentElement;
    if (!parent) return;

    // Split the text node at the start of the word
    const beforeWord = node.splitText(start);

    // Split the text node at the end of the word
    node.splitText(end - start);

    // Create a span for the misspelled word
    const span = document.createElement('span');
    span.className = 'js-spell-error-mark bg-red-100 dark:bg-red-700/30 cursor-pointer underline decoration-wavy decoration-red-500 dark:decoration-red-400 text-red-700 dark:text-red-400';
    span.setAttribute('data-word', word);
    span.setAttribute('data-error-index', String(this.errors.length - 1));
    span.textContent = beforeWord.textContent;

    // Replace the node with the span
    parent.replaceChild(span, beforeWord);

    // Add click handler
    span.addEventListener('click', () => {
      this.currentErrorIndex = parseInt(span.getAttribute('data-error-index') || '0');
      this.openErrorDialog();
    });

    // Store the marking
    this.markings.push(span);
  }

  /**
   * Update the dialog with spell check results
   */
  private updateResultsDialog(): void {
    if (!this.dialog) return;

    // Stop the checking state
    this.isChecking = false;

    if (this.errors.length === 0) {
      // No errors found - Rebuild dialog content
      this.dialog.innerHTML = ''; // Clear previous "checking" content

      const header = document.createElement('div');
      header.className = 'flex items-center justify-between mb-4';
      const title = document.createElement('h3');
      title.id = 'spell-grammar-dialog-title';
      title.className = 'font-semibold text-base m-0 text-gray-900 dark:text-slate-100';
      title.textContent = 'Spell Check Complete';
      const closeXButton = document.createElement('button');
      closeXButton.type = 'button';
      closeXButton.className = 'bg-transparent border-none text-lg cursor-pointer p-1 text-gray-500 dark:text-slate-400 hover:text-gray-700 dark:hover:text-slate-200 leading-none';
      closeXButton.setAttribute('aria-label', 'Close');
      closeXButton.textContent = '×';
      closeXButton.addEventListener('click', () => this.closeDialog());
      header.appendChild(title);
      header.appendChild(closeXButton);
      this.dialog.appendChild(header);

      const contentDiv = document.createElement('div');
      contentDiv.className = 'mb-5 text-sm text-gray-700 dark:text-slate-300';
      const p = document.createElement('p');
      p.textContent = 'No spelling or grammar issues found.';
      contentDiv.appendChild(p);
      this.dialog.appendChild(contentDiv);

      const actionsDiv = document.createElement('div');
      actionsDiv.className = 'flex justify-end items-center mt-5';
      const closeButtonMain = document.createElement('button');
      closeButtonMain.type = 'button';
      closeButtonMain.id = 'close-button';
      closeButtonMain.className = 'py-2 px-4 rounded border-none text-sm cursor-pointer font-medium bg-blue-600 hover:bg-blue-700 text-white';
      closeButtonMain.textContent = 'Close';
      closeButtonMain.addEventListener('click', () => this.closeDialog());
      actionsDiv.appendChild(closeButtonMain);
      this.dialog.appendChild(actionsDiv);

    } else {
      // Open the first error
      this.currentErrorIndex = 0;
      this.openErrorDialog(); // This will now rebuild the dialog for an error
      return; // openErrorDialog will handle further setup
    }

    // This part is now handled within the 'No errors found' block or by openErrorDialog
    // const closeButton = this.dialog.querySelector('#close-button');
    // closeButton?.addEventListener('click', () => {
    //   this.closeDialog();
    // });

    // const closeX = this.dialog.querySelector('.feather-spell-grammar-close');
    // closeX?.addEventListener('click', () => {
    //   this.closeDialog();
    // });

    // Handle escape key
    document.addEventListener('keydown', this.handleDialogKeydown);
  }

  /**
   * Open the dialog for a specific error
   */
  private openErrorDialog(): void {
    if (!this.dialog || this.errors.length === 0) return;

    const error = this.errors[this.currentErrorIndex];
    if (!this.dialog) return; // Should not happen if called correctly

    this.dialog.innerHTML = ''; // Clear previous content (e.g. "Checking..." or previous error)
    this.dialog.className = 'fixed top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[400px] bg-white dark:bg-slate-800 rounded-lg shadow-xl p-5 z-[9999] font-sans text-gray-900 dark:text-slate-200 flex flex-col';


    // Header
    const header = document.createElement('div');
    header.className = 'flex items-center justify-between mb-4';
    const title = document.createElement('h3');
    title.id = 'spell-grammar-dialog-title';
    title.className = 'font-semibold text-base m-0';
    title.textContent = 'Spelling Error';
    const closeXButton = document.createElement('button');
    closeXButton.type = 'button';
    closeXButton.className = 'bg-transparent border-none text-lg cursor-pointer p-1 text-gray-500 dark:text-slate-400 hover:text-gray-700 dark:hover:text-slate-200 leading-none';
    closeXButton.setAttribute('aria-label', 'Close');
    closeXButton.textContent = '×';
    header.appendChild(title);
    header.appendChild(closeXButton);
    this.dialog.appendChild(header);

    // Content
    const contentDiv = document.createElement('div');
    contentDiv.className = 'mb-5';

    const errorWordDiv = document.createElement('div');
    errorWordDiv.className = 'mb-4 text-sm leading-normal font-bold text-red-600 dark:text-red-400';
    errorWordDiv.textContent = error.word;
    contentDiv.appendChild(errorWordDiv);

    const suggestionsDiv = document.createElement('div');
    suggestionsDiv.className = 'mb-4';
    const suggestionsLabel = document.createElement('div');
    suggestionsLabel.className = 'block mb-2 font-medium text-sm text-gray-700 dark:text-slate-300';
    suggestionsLabel.textContent = 'Suggestions:';
    suggestionsDiv.appendChild(suggestionsLabel);

    const suggestionsList = document.createElement('ul');
    suggestionsList.className = 'flex flex-wrap gap-2 list-none p-0 m-0';
    if (error.suggestions.length > 0) {
      error.suggestions.forEach(suggestion => {
        const li = document.createElement('li');
        li.className = 'py-1.5 px-3 rounded bg-gray-100 dark:bg-slate-700 text-gray-700 dark:text-slate-200 text-sm cursor-pointer border border-transparent hover:bg-gray-200 dark:hover:bg-slate-600';
        li.dataset.suggestion = suggestion;
        li.textContent = suggestion;
        suggestionsList.appendChild(li);
      });
    } else {
      const noSuggestionsLi = document.createElement('li');
      noSuggestionsLi.className = 'italic text-gray-500 dark:text-slate-400 text-sm';
      noSuggestionsLi.textContent = 'No suggestions available.';
      suggestionsList.appendChild(noSuggestionsLi);
    }
    suggestionsDiv.appendChild(suggestionsList);
    contentDiv.appendChild(suggestionsDiv);
    this.dialog.appendChild(contentDiv);

    // Actions
    const actionsDiv = document.createElement('div');
    actionsDiv.className = 'flex justify-between items-center mt-5';

    const leftActions = document.createElement('div');
    leftActions.className = 'flex gap-2';
    const ignoreButtonElem = document.createElement('button');
    ignoreButtonElem.type = 'button'; ignoreButtonElem.id = 'ignore-button';
    ignoreButtonElem.className = 'py-2 px-4 rounded border-none text-sm cursor-pointer font-medium bg-gray-200 dark:bg-slate-600 hover:bg-gray-300 dark:hover:bg-slate-500 text-gray-700 dark:text-slate-200';
    ignoreButtonElem.textContent = 'Ignore';
    leftActions.appendChild(ignoreButtonElem);
    const ignoreAllButtonElem = document.createElement('button');
    ignoreAllButtonElem.type = 'button'; ignoreAllButtonElem.id = 'ignore-all-button';
    ignoreAllButtonElem.className = 'py-2 px-4 rounded border-none text-sm cursor-pointer font-medium bg-gray-200 dark:bg-slate-600 hover:bg-gray-300 dark:hover:bg-slate-500 text-gray-700 dark:text-slate-200';
    ignoreAllButtonElem.textContent = 'Ignore All';
    leftActions.appendChild(ignoreAllButtonElem);
    actionsDiv.appendChild(leftActions);

    const rightActions = document.createElement('div');
    rightActions.className = 'flex gap-2.5';
    const prevButtonElem = document.createElement('button');
    prevButtonElem.type = 'button'; prevButtonElem.id = 'prev-button';
    prevButtonElem.className = 'py-2 px-4 rounded border-none text-sm cursor-pointer font-medium bg-blue-600 hover:bg-blue-700 text-white disabled:bg-gray-400 dark:disabled:bg-slate-600 disabled:opacity-60 disabled:cursor-not-allowed';
    prevButtonElem.textContent = 'Previous';
    prevButtonElem.disabled = this.currentErrorIndex === 0;
    rightActions.appendChild(prevButtonElem);
    const nextButtonElem = document.createElement('button');
    nextButtonElem.type = 'button'; nextButtonElem.id = 'next-button';
    nextButtonElem.className = 'py-2 px-4 rounded border-none text-sm cursor-pointer font-medium bg-blue-600 hover:bg-blue-700 text-white disabled:bg-gray-400 dark:disabled:bg-slate-600 disabled:opacity-60 disabled:cursor-not-allowed';
    nextButtonElem.textContent = 'Next';
    nextButtonElem.disabled = this.currentErrorIndex >= this.errors.length - 1;
    rightActions.appendChild(nextButtonElem);
    actionsDiv.appendChild(rightActions);
    this.dialog.appendChild(actionsDiv);

    const progressDiv = document.createElement('div');
    progressDiv.className = 'text-xs text-gray-500 dark:text-slate-400 text-center mt-3';
    progressDiv.textContent = `${this.currentErrorIndex + 1} of ${this.errors.length}`;
    this.dialog.appendChild(progressDiv);

    // Setup event listeners
    this.setupErrorDialogEventListeners();
  }

  /**
   * Setup event listeners for the error dialog
   */
  private setupErrorDialogEventListeners(): void {
    if (!this.dialog) return;

    // Close button
    const closeButton = this.dialog.querySelector('.feather-spell-grammar-close');
    closeButton?.addEventListener('click', () => {
      this.closeDialog();
    });

    // Ignore button - skip to next error
    const ignoreButton = this.dialog.querySelector('#ignore-button');
    ignoreButton?.addEventListener('click', () => {
      this.goToNextError();
    });

    // Ignore all button - skip all instances of this word
    const ignoreAllButton = this.dialog.querySelector('#ignore-all-button');
    ignoreAllButton?.addEventListener('click', () => {
      const word = this.errors[this.currentErrorIndex].word.toLowerCase();

      // Add to dictionary so it won't be flagged again
      this.dictionary.add(word);

      // Remove all instances of this word from errors
      const newErrors = this.errors.filter(err => err.word.toLowerCase() !== word);

      // Remove markings for this word
      this.markings.forEach(span => {
        if (span.getAttribute('data-word')?.toLowerCase() === word) {
          const parent = span.parentNode;
          if (parent) {
            const text = document.createTextNode(span.textContent || '');
            parent.replaceChild(text, span);
          }
        }
      });

      // Update errors list
      this.errors = newErrors;

      // If no more errors, close dialog
      if (this.errors.length === 0) {
        this.closeDialog();
        return;
      }

      // Adjust current index if needed
      if (this.currentErrorIndex >= this.errors.length) {
        this.currentErrorIndex = this.errors.length - 1;
      }

      // Show next error
      this.openErrorDialog();
    });

    // Next button
    const nextButton = this.dialog.querySelector('#next-button');
    nextButton?.addEventListener('click', () => {
      this.goToNextError();
    });

    // Previous button
    const prevButton = this.dialog.querySelector('#prev-button');
    prevButton?.addEventListener('click', () => {
      if (this.currentErrorIndex > 0) {
        this.currentErrorIndex--;
        this.openErrorDialog();
      }
    });

    // Suggestion clicks
    const suggestions = this.dialog.querySelectorAll('.feather-spell-grammar-suggestion');
    suggestions.forEach(suggestion => {
      suggestion.addEventListener('click', () => {
        // Apply the suggestion
        const suggestedWord = (suggestion as HTMLElement).getAttribute('data-suggestion') || '';
        this.applyCorrection(suggestedWord);
      });
    });

    // Handle escape key
    document.addEventListener('keydown', this.handleDialogKeydown);
  }

  /**
   * Apply a correction to the misspelled word
   * @param correction The correction to apply
   */
  private applyCorrection(correction: string): void {
    if (!this.editor) return;

    // Find the marking for the current error
    const errorIndex = this.currentErrorIndex;
    const marking = this.markings.find(mark =>
      mark.getAttribute('data-error-index') === String(errorIndex)
    );

    if (marking && marking.parentNode) {
      // Create a text node with the correction
      const textNode = document.createTextNode(correction);

      // Replace the marking with the text node
      marking.parentNode.replaceChild(textNode, marking);

      // Remove this marking from our list
      this.markings = this.markings.filter((mark) => mark !== marking);

      // Trigger input event for history
      const editorElement = this.editor.getElement();
      if (editorElement) {
        editorElement.dispatchEvent(
          new InputEvent('input', { bubbles: true, cancelable: true })
        );
      }

      // Go to next error
      this.goToNextError();
    }
  }

  /**
   * Go to the next error
   */
  private goToNextError(): void {
    if (this.currentErrorIndex < this.errors.length - 1) {
      this.currentErrorIndex++;
      this.openErrorDialog();
    } else {
      // This was the last error, close dialog
      this.closeDialog();
    }
  }

  /**
   * Clear all markings from the editor
   */
  private clearMarkings(): void {
    // Query by the JS hook class
    const editorElement = this.editor?.getElement();
    if (editorElement) {
        const currentMarkings = editorElement.querySelectorAll('.js-spell-error-mark');
        currentMarkings.forEach(span => {
            if (span.parentNode) {
                const text = document.createTextNode(span.textContent || '');
                span.parentNode.replaceChild(text, span);
                // Normalize parent to merge adjacent text nodes
                span.parentNode.normalize();
            }
        });
    }

    this.markings = [];
    this.errors = [];
  }

  /**
   * Handle keydown events for the dialog
   */
  private handleDialogKeydown = (event: KeyboardEvent): void => {
    if (event.key === 'Escape') {
      this.closeDialog();
    }
  };

  /**
   * Close the dialog
   */
  private closeDialog(): void {
    if (!this.isDialogOpen) return;

    // Remove backdrop
    const backdrop = document.querySelector('.feather-spell-grammar-backdrop');
    if (backdrop && backdrop.parentNode) {
      backdrop.parentNode.removeChild(backdrop);
    }

    // Remove dialog
    if (this.dialog && this.dialog.parentNode) {
      this.dialog.parentNode.removeChild(this.dialog);
    }

    this.dialog = null;
    this.isDialogOpen = false;
    this.isChecking = false;

    // Remove event listener
    document.removeEventListener('keydown', this.handleDialogKeydown);

    // Focus back to editor
    if (this.editor) {
      this.editor.focus();
    }
  }

  /**
   * Clean up when destroyed
   */
  public destroy(): void {
    super.destroy();

    // Clear any markings
    this.clearMarkings();

    // Close dialog if open
    if (this.isDialogOpen) {
      this.closeDialog();
    }
  }
}

// Create and export the plugin instance directly
const plugin = new SpellGrammarPlugin();

// Export the plugin ID for registration in the plugin manager
export const pluginId = config.id;

// Default export for tree-shaking
export default plugin;
